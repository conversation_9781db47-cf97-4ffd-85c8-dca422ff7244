# Testing Strategy - LinkUp Plugin

## Testing Philosophy

Our testing approach follows the **Test Pyramid** principle with emphasis on:
1. **Unit Tests** (70%) - Fast, isolated, comprehensive
2. **Integration Tests** (20%) - API endpoints, database interactions
3. **End-to-End Tests** (10%) - Critical user journeys

## Testing Framework Stack

### Backend (Python/Flask)
- **Unit Testing**: pytest, pytest-cov
- **API Testing**: pytest-flask, requests-mock
- **Database Testing**: pytest-postgresql, factory-boy
- **Mocking**: unittest.mock, responses
- **Performance**: locust, pytest-benchmark

### WordPress Plugin (PHP)
- **Unit Testing**: PHPUnit
- **Integration Testing**: WP-CLI testing framework
- **Browser Testing**: Codeception
- **Code Quality**: PHP_CodeSniffer, PHPStan

### AI/ML Components
- **Model Testing**: pytest, scikit-learn test utilities
- **Data Testing**: great-expectations, pandas testing
- **Performance**: memory-profiler, line-profiler

## Test Categories

### 1. Unit Tests

#### Backend API Tests
```python
# Example: Content Analysis Service Tests
class TestContentAnalysisService:
    def test_extract_keywords_from_text(self):
        service = ContentAnalysisService()
        text = "Python web development with Flask framework"
        keywords = service.extract_keywords(text)
        
        assert "python" in [k.lower() for k in keywords]
        assert "flask" in [k.lower() for k in keywords]
        assert len(keywords) >= 2

    def test_calculate_content_quality_score(self):
        service = ContentAnalysisService()
        content = {
            'word_count': 1500,
            'readability_score': 8.5,
            'keyword_density': 0.02,
            'uniqueness_score': 0.95
        }
        score = service.calculate_quality_score(content)
        
        assert 0 <= score <= 10
        assert isinstance(score, float)

    @patch('app.services.content_service.spacy.load')
    def test_analyze_content_with_mock_nlp(self, mock_spacy):
        # Test with mocked spaCy to avoid loading large models
        mock_nlp = Mock()
        mock_spacy.return_value = mock_nlp
        
        service = ContentAnalysisService()
        result = service.analyze_content("Test content")
        
        assert result is not None
        mock_spacy.assert_called_once()
```

#### WordPress Plugin Tests
```php
<?php
class TestContentExtractor extends WP_UnitTestCase {
    
    public function test_extract_post_content() {
        $post_id = $this->factory->post->create([
            'post_title' => 'Test Post',
            'post_content' => 'This is test content with keywords.',
            'post_status' => 'publish'
        ]);
        
        $extractor = new ContentExtractor();
        $content = $extractor->extract_post_content($post_id);
        
        $this->assertArrayHasKey('title', $content);
        $this->assertArrayHasKey('content', $content);
        $this->assertEquals('Test Post', $content['title']);
    }
    
    public function test_validate_content_before_analysis() {
        $extractor = new ContentExtractor();
        
        // Test valid content
        $valid_content = ['title' => 'Test', 'content' => 'Valid content'];
        $this->assertTrue($extractor->validate_content($valid_content));
        
        // Test invalid content
        $invalid_content = ['title' => '', 'content' => ''];
        $this->assertFalse($extractor->validate_content($invalid_content));
    }
}
```

### 2. Integration Tests

#### API Endpoint Tests
```python
class TestBacklinkAPI:
    def test_create_backlink_request(self, client, auth_headers):
        data = {
            'source_website_id': 1,
            'target_website_id': 2,
            'target_url': 'https://example.com/post',
            'preferred_anchor_text': 'great article'
        }
        
        response = client.post('/api/backlinks', 
                             json=data, 
                             headers=auth_headers)
        
        assert response.status_code == 201
        assert response.json['success'] is True
        assert 'id' in response.json['data']

    def test_get_backlinks_with_filters(self, client, auth_headers):
        response = client.get('/api/backlinks?status=active&limit=10',
                            headers=auth_headers)
        
        assert response.status_code == 200
        assert len(response.json['data']['backlinks']) <= 10
        
        for backlink in response.json['data']['backlinks']:
            assert backlink['status'] == 'active'
```

#### Database Integration Tests
```python
class TestDatabaseOperations:
    def test_website_creation_and_retrieval(self, db_session):
        website = Website(
            domain='test.com',
            title='Test Site',
            user_id=1
        )
        db_session.add(website)
        db_session.commit()
        
        retrieved = db_session.query(Website).filter_by(domain='test.com').first()
        assert retrieved is not None
        assert retrieved.title == 'Test Site'

    def test_backlink_relationship_integrity(self, db_session):
        # Test foreign key relationships
        source = Website(domain='source.com', user_id=1)
        target = Website(domain='target.com', user_id=2)
        db_session.add_all([source, target])
        db_session.commit()
        
        backlink = Backlink(
            source_website_id=source.id,
            target_website_id=target.id,
            anchor_text='test link'
        )
        db_session.add(backlink)
        db_session.commit()
        
        assert backlink.source_website.domain == 'source.com'
        assert backlink.target_website.domain == 'target.com'
```

### 3. End-to-End Tests

#### Critical User Journeys
```python
class TestUserJourneys:
    def test_complete_onboarding_flow(self, browser):
        # 1. Install plugin
        browser.visit('/wp-admin/plugin-install.php')
        browser.find_by_text('LinkUp Plugin').click()
        browser.find_by_text('Install Now').click()
        browser.find_by_text('Activate').click()
        
        # 2. Complete setup
        assert browser.is_text_present('Welcome to LinkUp')
        browser.fill('website_description', 'My test blog about technology')
        browser.find_by_text('Start Analysis').click()
        
        # 3. Wait for analysis completion
        browser.wait_for_text('Analysis Complete', timeout=300)
        
        # 4. Check dashboard
        browser.visit('/wp-admin/admin.php?page=linkup-dashboard')
        assert browser.is_text_present('Backlink Opportunities')

    def test_backlink_approval_workflow(self, browser, api_client):
        # Setup: Create a backlink match via API
        match_data = {
            'source_website_id': 1,
            'target_website_id': 2,
            'compatibility_score': 0.9
        }
        response = api_client.post('/api/backlinks/matches', json=match_data)
        match_id = response.json['data']['id']
        
        # User approves match in WordPress admin
        browser.visit(f'/wp-admin/admin.php?page=linkup-matches')
        browser.find_by_css(f'[data-match-id="{match_id}"] .approve-btn').click()
        
        # Verify backlink was created
        browser.wait_for_text('Backlink approved successfully')
        
        # Check via API that backlink exists
        backlinks = api_client.get('/api/backlinks').json['data']['backlinks']
        assert any(bl['match_id'] == match_id for bl in backlinks)
```

### 4. Performance Tests

#### Load Testing
```python
from locust import HttpUser, task, between

class LinkUpAPIUser(HttpUser):
    wait_time = between(1, 3)
    
    def on_start(self):
        # Login and get auth token
        response = self.client.post('/api/auth/login', json={
            'email': '<EMAIL>',
            'password': 'testpass'
        })
        self.token = response.json()['data']['access_token']
        self.headers = {'Authorization': f'Bearer {self.token}'}
    
    @task(3)
    def get_backlinks(self):
        self.client.get('/api/backlinks', headers=self.headers)
    
    @task(2)
    def get_analytics(self):
        self.client.get('/api/analytics/dashboard', headers=self.headers)
    
    @task(1)
    def create_backlink(self):
        data = {
            'source_website_id': 1,
            'target_website_id': 2,
            'target_url': 'https://example.com/test'
        }
        self.client.post('/api/backlinks', json=data, headers=self.headers)
```

#### AI Model Performance Tests
```python
class TestAIPerformance:
    def test_content_analysis_speed(self):
        service = ContentAnalysisService()
        content = "Long content text..." * 1000  # ~10k words
        
        start_time = time.time()
        result = service.analyze_content(content)
        end_time = time.time()
        
        # Should complete within 30 seconds for 10k words
        assert (end_time - start_time) < 30
        assert result is not None

    def test_matching_algorithm_scalability(self):
        service = MatchingService()
        
        # Test with 1000 websites
        websites = [create_test_website() for _ in range(1000)]
        
        start_time = time.time()
        matches = service.find_matches(websites[0], websites[1:])
        end_time = time.time()
        
        # Should complete within 60 seconds for 1000 comparisons
        assert (end_time - start_time) < 60
        assert len(matches) > 0
```

### 5. Security Tests

#### Authentication & Authorization
```python
class TestSecurity:
    def test_unauthorized_access_blocked(self, client):
        response = client.get('/api/backlinks')
        assert response.status_code == 401
        
        response = client.post('/api/backlinks', json={})
        assert response.status_code == 401

    def test_sql_injection_prevention(self, client, auth_headers):
        malicious_input = "'; DROP TABLE users; --"
        response = client.get(f'/api/websites?search={malicious_input}',
                            headers=auth_headers)
        
        # Should not cause server error
        assert response.status_code in [200, 400]
        
        # Database should still be intact
        response = client.get('/api/websites', headers=auth_headers)
        assert response.status_code == 200

    def test_xss_prevention(self, client, auth_headers):
        xss_payload = "<script>alert('xss')</script>"
        data = {
            'domain': 'test.com',
            'title': xss_payload,
            'description': 'Normal description'
        }
        
        response = client.post('/api/websites', json=data, headers=auth_headers)
        
        if response.status_code == 201:
            # Check that script tags are escaped/removed
            website = response.json['data']
            assert '<script>' not in website['title']
```

## Test Data Management

### Fixtures and Factories
```python
# conftest.py
@pytest.fixture
def sample_website():
    return {
        'domain': 'example.com',
        'title': 'Example Blog',
        'description': 'A blog about examples',
        'category': 'technology',
        'language': 'en'
    }

@pytest.fixture
def auth_headers(client):
    # Create test user and get token
    response = client.post('/api/auth/register', json={
        'email': '<EMAIL>',
        'password': 'testpass123',
        'website_url': 'https://test.com'
    })
    token = response.json['data']['access_token']
    return {'Authorization': f'Bearer {token}'}

# Factory for creating test data
class WebsiteFactory:
    @staticmethod
    def create(domain=None, **kwargs):
        defaults = {
            'domain': domain or f'test{random.randint(1000, 9999)}.com',
            'title': 'Test Website',
            'description': 'Test description',
            'category': 'technology',
            'language': 'en'
        }
        defaults.update(kwargs)
        return Website(**defaults)
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Test Suite

on: [push, pull_request]

jobs:
  test-backend:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.9'
    
    - name: Install dependencies
      run: |
        pip install -r requirements.txt
        pip install -r requirements-test.txt
    
    - name: Run tests
      run: |
        pytest --cov=app --cov-report=xml
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3

  test-wordpress:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Setup WordPress test environment
      run: |
        bash bin/install-wp-tests.sh wordpress_test root '' localhost latest
    
    - name: Run PHPUnit tests
      run: |
        phpunit
```

## Test Coverage Goals

### Minimum Coverage Requirements
- **Backend API**: 85% line coverage
- **WordPress Plugin**: 80% line coverage
- **AI/ML Components**: 75% line coverage
- **Critical Paths**: 95% coverage

### Coverage Reporting
- Automated coverage reports in CI/CD
- Coverage badges in README
- Coverage trends tracking
- Failed builds on coverage drops

---

*This testing strategy ensures high-quality, reliable software through comprehensive automated testing at all levels.*
