import spacy
from sklearn.feature_extraction.text import CountVectorizer

# Load spacy model
nlp = spacy.load('en_core_web_sm')

def analyze_content(content):
    doc = nlp(content)
    
    # Extract named entities and keywords
    entities = [(ent.text, ent.label_) for ent in doc.ents]
    keywords = [token.lemma_ for token in doc if token.is_alpha and not token.is_stop]
    
    return {
        'entities': entities,
        'keywords': keywords
    }

def analyze_backlink_pattern(website_content, backlink_content):
    vectorizer = CountVectorizer().fit([website_content, backlink_content])
    similarity = vectorizer.transform([website_content, backlink_content]).toarray()
    
    # Simple similarity comparison
    return similarity[0].dot(similarity[1])
