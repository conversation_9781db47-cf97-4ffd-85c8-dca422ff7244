from flask import Flask, request, jsonify
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'mysql://user:password@localhost/database_name'
db = SQLAlchemy(app)

# Define the Models
class Website(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    url = db.Column(db.String(255), unique=True, nullable=False)
    site_title = db.Column(db.String(255))
    tagline = db.Column(db.String(255))
    is_active = db.Column(db.Boolean, default=True)
    backlinks_given = db.Column(db.Integer, default=0)
    backlinks_received = db.Column(db.Integer, default=0)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

class Backlink(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    source_website_id = db.Column(db.Integer, db.ForeignKey('website.id'))
    target_website_id = db.Column(db.Integer, db.ForeignKey('website.id'))
    backlink_url = db.Column(db.String(255))
    is_follow = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, server_default=db.func.now())

class AIAnalysis(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    website_id = db.Column(db.Integer, db.ForeignKey('website.id'))
    insights = db.Column(db.Text)
    suggestions = db.Column(db.Text)
    analyzed_at = db.Column(db.DateTime, server_default=db.func.now())

# API to register a website
@app.route('/register_website', methods=['POST'])
def register_website():
    data = request.get_json()
    url = data['url']
    site_title = data.get('site_title')
    tagline = data.get('tagline')
    
    website = Website(url=url, site_title=site_title, tagline=tagline)
    db.session.add(website)
    db.session.commit()
    
    return jsonify({'status': 'Website registered successfully', 'id': website.id})

# API to track backlinks
@app.route('/track_backlink', methods=['POST'])
def track_backlink():
    data = request.get_json()
    source_url = data['source_url']
    target_url = data['target_url']
    backlink_url = data['backlink_url']
    is_follow = data.get('is_follow', True)
    
    source_website = Website.query.filter_by(url=source_url).first()
    target_website = Website.query.filter_by(url=target_url).first()
    
    if not source_website or not target_website:
        return jsonify({'error': 'Website not found'}), 404
    
    backlink = Backlink(source_website_id=source_website.id, target_website_id=target_website.id, backlink_url=backlink_url, is_follow=is_follow)
    db.session.add(backlink)
    
    source_website.backlinks_given += 1
    target_website.backlinks_received += 1
    
    db.session.commit()
    
    return jsonify({'status': 'Backlink tracked successfully'})

# Admin login for centralized server
@app.route('/admin_login', methods=['POST'])
def admin_login():
    data = request.get_json()
    username = data['username']
    password = data['password']
    
    admin = AdminUser.query.filter_by(username=username).first()
    if admin and check_password_hash(admin.password_hash, password):
        return jsonify({'status': 'Login successful'})
    return jsonify({'error': 'Invalid credentials'}), 401

if __name__ == '__main__':
    db.create_all()
    app.run(debug=True)
