# API Specifications - LinkUp Plugin

## API Overview

The LinkUp API is a RESTful service that handles all backend operations for the WordPress plugin. It provides endpoints for user management, website registration, content analysis, backlink management, and analytics.

**Base URL**: `https://api.linkup-plugin.com/v1`  
**Authentication**: JWT Bearer tokens  
**Content Type**: `application/json`  
**Rate Limiting**: 1000 requests/hour per user

## Authentication Endpoints

### POST /auth/register
Register a new user account.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123",
  "website_url": "https://example.com",
  "website_title": "My Awesome Blog"
}
```

**Response (201)**:
```json
{
  "success": true,
  "data": {
    "user_id": 123,
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600
  }
}
```

### POST /auth/login
Authenticate existing user.

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "expires_in": 3600,
    "user": {
      "id": 123,
      "email": "<EMAIL>",
      "plan": "free"
    }
  }
}
```

### POST /auth/refresh
Refresh access token.

**Request Body**:
```json
{
  "refresh_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
}
```

## Website Management Endpoints

### GET /websites
Get all websites for authenticated user.

**Headers**: `Authorization: Bearer {access_token}`

**Response (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 456,
      "domain": "example.com",
      "title": "My Awesome Blog",
      "description": "A blog about awesome things",
      "category": "lifestyle",
      "language": "en",
      "status": "active",
      "analysis_status": "completed",
      "backlinks_count": 15,
      "created_at": "2024-01-15T10:30:00Z"
    }
  ]
}
```

### POST /websites
Register a new website.

**Request Body**:
```json
{
  "domain": "newblog.com",
  "title": "New Blog",
  "description": "My new blog about technology",
  "category": "technology",
  "language": "en"
}
```

**Response (201)**:
```json
{
  "success": true,
  "data": {
    "id": 789,
    "domain": "newblog.com",
    "status": "pending_analysis",
    "message": "Website registered successfully. Analysis will begin shortly."
  }
}
```

### POST /websites/{id}/analyze
Trigger content analysis for a website.

**Response (202)**:
```json
{
  "success": true,
  "data": {
    "message": "Analysis started",
    "job_id": "analysis_789_20240115",
    "estimated_completion": "2024-01-15T11:00:00Z"
  }
}
```

## Content Analysis Endpoints

### GET /websites/{id}/analysis
Get content analysis results.

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "website_id": 456,
    "analysis_date": "2024-01-15T10:45:00Z",
    "content_summary": {
      "total_posts": 150,
      "total_words": 75000,
      "average_readability": 8.2,
      "primary_language": "en"
    },
    "keywords": [
      {
        "keyword": "web development",
        "frequency": 45,
        "relevance_score": 0.92
      },
      {
        "keyword": "javascript",
        "frequency": 38,
        "relevance_score": 0.87
      }
    ],
    "categories": [
      {
        "category": "technology",
        "confidence": 0.95
      },
      {
        "category": "programming",
        "confidence": 0.78
      }
    ],
    "quality_metrics": {
      "overall_score": 8.5,
      "content_depth": 8.2,
      "readability": 8.8,
      "uniqueness": 8.7
    }
  }
}
```

### GET /websites/{id}/keyword-gaps
Get keyword gap analysis and content suggestions.

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "analysis_date": "2024-01-15T10:45:00Z",
    "keyword_gaps": [
      {
        "keyword": "react hooks tutorial",
        "search_volume": 12000,
        "difficulty": 45,
        "opportunity_score": 8.3,
        "competitor_coverage": 3,
        "suggested_content_type": "tutorial"
      }
    ],
    "content_suggestions": [
      {
        "title": "Complete Guide to React Hooks",
        "target_keywords": ["react hooks", "useState", "useEffect"],
        "estimated_traffic": 2500,
        "content_outline": [
          "Introduction to React Hooks",
          "useState Hook Explained",
          "useEffect Hook Deep Dive"
        ]
      }
    ]
  }
}
```

## Backlink Management Endpoints

### GET /backlinks
Get all backlinks for user's websites.

**Query Parameters**:
- `status`: filter by status (pending, active, removed)
- `website_id`: filter by specific website
- `limit`: number of results (default: 50)
- `offset`: pagination offset

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "backlinks": [
      {
        "id": 1001,
        "source_website": {
          "id": 789,
          "domain": "techblog.com",
          "title": "Tech Blog"
        },
        "target_website": {
          "id": 456,
          "domain": "example.com",
          "title": "My Awesome Blog"
        },
        "anchor_text": "awesome web development tips",
        "target_url": "https://example.com/web-dev-tips",
        "status": "active",
        "relevance_score": 0.89,
        "created_at": "2024-01-10T14:20:00Z",
        "delivered_at": "2024-01-12T09:15:00Z"
      }
    ],
    "pagination": {
      "total": 25,
      "limit": 50,
      "offset": 0,
      "has_more": false
    }
  }
}
```

### GET /backlinks/matches
Get potential backlink matches for user's websites.

**Response (200)**:
```json
{
  "success": true,
  "data": [
    {
      "id": 2001,
      "partner_website": {
        "id": 999,
        "domain": "designblog.com",
        "title": "Design Blog",
        "category": "design"
      },
      "compatibility_score": 0.92,
      "match_reasons": [
        "Similar content categories",
        "Complementary keywords",
        "Similar domain authority"
      ],
      "estimated_value": 8.5,
      "mutual_benefit": true,
      "status": "pending_approval"
    }
  ]
}
```

### POST /backlinks
Create a new backlink request.

**Request Body**:
```json
{
  "source_website_id": 456,
  "target_website_id": 789,
  "target_url": "https://example.com/specific-post",
  "preferred_anchor_text": "great design tips",
  "notes": "This would be a perfect match for our design section"
}
```

### PUT /backlinks/{id}/status
Update backlink status (approve, reject, etc.).

**Request Body**:
```json
{
  "status": "approved",
  "notes": "Great content match"
}
```

## Analytics Endpoints

### GET /analytics/dashboard
Get dashboard analytics for user's websites.

**Query Parameters**:
- `website_id`: specific website (optional)
- `period`: time period (7d, 30d, 90d, 1y)

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "period": "30d",
    "summary": {
      "total_backlinks": 45,
      "new_backlinks": 12,
      "traffic_increase": 23.5,
      "ranking_improvements": 8,
      "average_relevance_score": 0.87
    },
    "traffic_data": [
      {
        "date": "2024-01-01",
        "organic_traffic": 1250,
        "backlink_traffic": 180
      }
    ],
    "ranking_data": [
      {
        "keyword": "web development",
        "previous_position": 15,
        "current_position": 8,
        "change": 7
      }
    ]
  }
}
```

### GET /analytics/performance
Get detailed performance metrics.

**Response (200)**:
```json
{
  "success": true,
  "data": {
    "roi_metrics": {
      "estimated_traffic_value": 2500,
      "cost_per_backlink": 0,
      "roi_percentage": "infinite"
    },
    "quality_metrics": {
      "average_domain_authority": 45.2,
      "average_relevance_score": 0.89,
      "spam_score": 0.02
    },
    "velocity_metrics": {
      "current_velocity": 2.3,
      "optimal_velocity": 2.8,
      "velocity_health": "good"
    }
  }
}
```

## Error Responses

### Standard Error Format
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid email format",
    "details": {
      "field": "email",
      "value": "invalid-email"
    }
  }
}
```

### Common Error Codes
- `AUTHENTICATION_REQUIRED` (401)
- `INSUFFICIENT_PERMISSIONS` (403)
- `RESOURCE_NOT_FOUND` (404)
- `VALIDATION_ERROR` (422)
- `RATE_LIMIT_EXCEEDED` (429)
- `INTERNAL_SERVER_ERROR` (500)

## Rate Limiting

### Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: **********
```

### Rate Limits by Plan
- **Free**: 1,000 requests/hour
- **Pro**: 5,000 requests/hour  
- **Agency**: 25,000 requests/hour

## Webhooks

### Configuration
```json
{
  "url": "https://yoursite.com/webhook",
  "events": ["backlink.created", "analysis.completed"],
  "secret": "webhook_secret_key"
}
```

### Event Types
- `backlink.created`
- `backlink.approved`
- `backlink.removed`
- `analysis.completed`
- `match.found`

---

*This API specification will be updated as new features are developed and existing endpoints are enhanced.*
