A subset of lib2to3 taken from Python 3.7.0b2.
Commit hash: 9c17e3a1987004b8bcfbe423953aad84493a7984

Reasons for forking:
- consistent handling of f-strings for users of Python < 3.6.2
- backport of BPO-33064 that fixes parsing files with trailing commas after
  *args and **kwargs
- backport of GH-6143 that restores the ability to reformat legacy usage of
  `async`
- support all types of string literals
- better ability to debug (better reprs)
- INDENT and DEDENT don't hold whitespace and comment prefixes
- ability to Cythonize

Change Log:
- Changes default logger used by Driver
- Backported the following upstream parser changes:
  - "bpo-42381: Allow walrus in set literals and set comprehensions (GH-23332)"
    https://github.com/python/cpython/commit/cae60187cf7a7b26281d012e1952fafe4e2e97e9
  - "bpo-42316: Allow unparenthesized walrus operator in indexes (GH-23317)"
    https://github.com/python/cpython/commit/b0aba1fcdc3da952698d99aec2334faa79a8b68c
- Tweaks to help mypyc compile faster code (including inlining type information,
  "Final-ing", etc.)
